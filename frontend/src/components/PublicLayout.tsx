import React from 'react';
import { Link } from 'react-router-dom';
import { Trophy, LogIn, UserPlus } from 'lucide-react';
import SharedNavbar from './SharedNavbar';
import './Homepage.css';

interface PublicLayoutProps {
  children: React.ReactNode;
}

const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <SharedNavbar variant="plaza" />

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto">
        {children}
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500 text-sm">
            <p>&copy; 2025 P-Box Update Server. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicLayout;
