import React, { useState, useEffect } from 'react';
import {
    ArrowLeft,
    Calendar,
    Package,
    Crown
} from 'lucide-react';
import { apiClient } from '../api';
import { generateAvatarUrl } from '../utils/avatar';
import SharedNavbar from './SharedNavbar';
import './Homepage.css';
import type { ProjectWithCreator } from '../types';

interface UserProjectsProps {
    userId: number;
    username: string;
    onBack: () => void;
}

const UserProjects: React.FC<UserProjectsProps> = ({ userId, username, onBack }) => {
    const [projects, setProjects] = useState<ProjectWithCreator[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadUserProjects();
    }, [userId]);

    const loadUserProjects = async () => {
        try {
            setLoading(true);
            const data = await apiClient.getUserHighScoreProjects(userId);
            setProjects(data);
        } catch (error) {
            console.error('Failed to load user projects:', error);
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('zh-CN');
    };



    const ProjectCard: React.FC<{ project: ProjectWithCreator }> = ({ project }) => (
        <div className={`rounded-xl shadow-sm border overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105 ${
            project.is_featured 
                ? 'border-yellow-300 bg-gradient-to-br from-yellow-50 to-orange-50' 
                : 'border-gray-200 bg-white'
        }`}>
            {/* 官方精选标识 */}
            {project.is_featured && (
                <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-4 py-2 text-sm font-medium flex items-center">
                    <Crown className="h-4 w-4 mr-2" />
                    官方精选
                </div>
            )}
            
            <div className="p-6">
                {/* 项目标题 */}
                <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                        {project.name}
                    </h3>
                </div>

                {/* 项目描述 */}
                <div className="mb-4">
                    {project.description ? (
                        <p className="text-gray-600 text-sm line-clamp-3">
                            {project.description}
                        </p>
                    ) : (
                        <p className="text-gray-400 text-sm italic">
                            暂无项目描述
                        </p>
                    )}
                </div>

                {/* 创建时间 */}
                <div className="flex items-center justify-end text-xs text-gray-400">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(project.created_at)}
                </div>
            </div>
        </div>
    );

    return (
        <div className="min-h-screen bg-gray-50">
            {/* 导航栏 */}
            <SharedNavbar variant="plaza" />

            <div className="p-6">
                {/* 头部 */}
                <div className="mb-6">
                <button
                    onClick={onBack}
                    className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
                >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    返回项目广场
                </button>
                
                <div className="flex items-center space-x-4">
                    {projects.length > 0 && projects[0].creator_avatar_url ? (
                        <img
                            src={projects[0].creator_avatar_url}
                            alt={username}
                            className="h-16 w-16 rounded-full"
                        />
                    ) : (
                        <img
                            src={generateAvatarUrl(username)}
                            alt={username}
                            className="h-16 w-16 rounded-full"
                        />
                    )}
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{username} 的公开项目</h1>
                        <p className="text-gray-600">共 {projects.length} 个公开项目</p>
                    </div>
                </div>
            </div>

            {loading ? (
                <div className="text-center py-12">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-gray-600">加载中...</p>
                </div>
            ) : projects.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {projects.map((project) => (
                        <ProjectCard key={project.id} project={project} />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <Package className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">暂无公开项目</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        该用户还没有公开项目
                    </p>
                </div>
            )}
            </div>
        </div>
    );
};

export default UserProjects;
