/* Homepage 组件样式 */
.homepage {
  /* 基础样式变量 */
  --primary-color: #396cd8;
  --primary-dark: #2a5bb9;
  --secondary-color: #4CAF50;
  --text-color: #333;
  --text-light: #666;
  --background-color: #f6f6f6;
  --white: #ffffff;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

.homepage * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.homepage {
  font-family: 'Inter', 'Avenir', 'Helvetica', 'Arial', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--background-color);
}

.homepage .container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.homepage section {
  padding: 80px 0;
}

.homepage .section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.homepage .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

/* 导航栏样式 */
.homepage header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: var(--transition);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.homepage header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow);
}


.homepage header nav {
  padding: 10px 0 !important;
}

.homepage header.scrolled nav {
  padding: 10px 0;
}

.homepage nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.homepage .logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.homepage .logo img {
  width: 40px;
  height: 40px;
}

.homepage .nav-links {
  display: flex;
  gap: 30px;
  align-items: center;
}

.homepage .nav-links a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.homepage .nav-links a:hover {
  color: var(--primary-color);
}

.homepage .nav-links a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition);
}

.homepage .nav-links a:hover::after {
  width: 100%;
}

.homepage .nav-admin-link {
  background: var(--primary-color);
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: var(--transition);
}

.homepage .nav-admin-link:hover {
  background: var(--primary-dark);
  color: white !important;
}

.homepage .nav-admin-link::after {
  display: none;
}

.homepage .menu-toggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--primary-color);
}

/* 用户菜单样式 */
.homepage .user-menu {
  position: relative;
}

.homepage .user-menu button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: var(--transition);
}

.homepage .user-menu button:hover {
  background: rgba(57, 108, 216, 0.1);
}

.plaza-header .user-menu {
  position: relative;
}

.plaza-header .user-menu button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: var(--transition);
}

.plaza-header .user-menu button:hover {
  background: rgba(57, 108, 216, 0.1);
}

/* 项目广场页面导航栏样式 */
.plaza-header {
  position: static;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.plaza-header nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.plaza-header .logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.plaza-header .logo-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: var(--primary-color);
}

.plaza-header .logo img {
  width: 40px;
  height: 40px;
}

.plaza-header .nav-links {
  display: flex;
  gap: 30px;
  align-items: center;
}

.plaza-header .nav-links a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.plaza-header .nav-links a:hover {
  color: var(--primary-color);
}

.plaza-header .nav-links a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition);
}

.plaza-header .nav-links a:hover::after {
  width: 100%;
}

.plaza-header .nav-admin-link {
  background: var(--primary-color);
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: var(--transition);
}

.plaza-header .nav-admin-link:hover {
  background: var(--primary-dark);
  color: white !important;
}

.plaza-header .nav-admin-link::after {
  display: none;
}

.plaza-header .menu-toggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--primary-color);
}

/* 英雄区域样式 */
.homepage .hero {
  padding-top: 150px;
  padding-bottom: 100px;
  background: linear-gradient(135deg, #f6f6f6 0%, #e9f0ff 100%);
}

.homepage .hero .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.homepage .hero-content {
  flex: 1;
  max-width: 600px;
}

.homepage .hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  color: var(--text-color);
}

.homepage .hero-content p {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 30px;
}

.homepage .hero-buttons {
  display: flex;
  gap: 15px;
}

.homepage .btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  border: none;
  outline: none;
  box-shadow: var(--shadow);
}

.homepage .primary-btn {
  background-color: var(--primary-color);
  color: var(--white);
}

.homepage .primary-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.homepage .secondary-btn {
  background-color: var(--white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.homepage .secondary-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.homepage .hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.homepage .hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.homepage .hero-image img:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 功能特点样式 */
.homepage .features {
  background: var(--white);
}

.homepage .features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.homepage .feature-card {
  background: var(--white);
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: var(--shadow);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.homepage .feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.homepage .feature-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.homepage .feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--text-color);
}

.homepage .feature-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* 界面展示样式 */
.homepage .screenshots {
  background-color: #f0f4ff;
}

.homepage .screenshot-container {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  margin-bottom: 40px !important; /* 为图片说明文字留出空间 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.homepage .screenshot-slider {
  position: relative;
  overflow: hidden;
  flex: 1;
  width: 100%;
  /* 使用固定的宽高比来保持容器大小 */
  padding-bottom: 56.25%; /* 16:9 的宽高比 */
}

.homepage .screenshot {
  max-width: 100%;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateX(100%); /* 初始位置在右侧 */
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  pointer-events: none; /* 防止非活动幻灯片接收点击事件 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.homepage .screenshot.active {
  opacity: 1;
  transform: translateX(0); /* 活动状态位于中央 */
  pointer-events: auto; /* 活动幻灯片可以接收点击事件 */
}

.homepage .screenshot.prev {
  transform: translateX(-100%); /* 上一张幻灯片位于左侧 */
  opacity: 0;
}

.homepage .screenshot.next {
  transform: translateX(100%); /* 下一张幻灯片位于右侧 */
  opacity: 0;
}

.homepage .screenshot img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 10px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.homepage .screenshot img:hover {
  transform: scale(1.01);
}

.homepage .screenshot p {
  margin-top: 15px;
  color: var(--text-light);
  position: absolute;
  bottom: -30px;
  left: 0;
  width: 100%;
  text-align: center;
}

.homepage .slider-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--white);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: var(--transition);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.homepage .slider-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.homepage .prev-btn {
  left: -20px;
}

.homepage .next-btn {
  right: -20px;
}

/* 下载区域样式 */
.homepage .download {
  background: linear-gradient(135deg, #396cd8 0%, #2a5bb9 100%);
  color: var(--white);
}

.homepage .download .section-title {
  color: var(--white);
}

.homepage .download .section-title::after {
  background-color: var(--white);
}

.homepage .download-options {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.homepage .download-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--white);
  color: var(--primary-color);
  padding: 20px 40px;
  border-radius: 10px;
  text-decoration: none;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.homepage .download-btn i {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.homepage .download-btn span {
  font-weight: 600;
}

.homepage .download-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* 联系我们样式 */
.homepage .contact {
  background: var(--background-color);
}

.homepage .contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.homepage .contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.homepage .contact-item i {
  font-size: 2rem;
  color: var(--secondary-color);
}

.homepage .contact-item p {
  font-size: 1.2rem;
  font-weight: 600;
}

.homepage .contact-description {
  color: var(--text-light);
  line-height: 1.6;
}

.homepage .qr-code {
  text-align: center;
}

.homepage .qr-placeholder {
  width: 200px;
  height: 200px;
  background: var(--white);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: var(--shadow);
}

.homepage .qr-placeholder img {
  width: 180px;
  height: 180px;
  border-radius: 8px;
}

/* 页脚样式 */
.homepage footer {
  background: var(--text-color);
  color: var(--white);
  padding: 40px 0;
}

.homepage .footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.homepage .footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: bold;
}

.homepage .footer-logo img {
  width: 30px;
  height: 30px;
}

.homepage .copyright {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
}

.homepage .copyright:hover {
  color: var(--white);
}

/* 返回顶部按钮 */
.homepage .back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
}

.homepage .back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.homepage .back-to-top:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .homepage .nav-links {
    display: none;
  }

  .homepage .nav-links.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 80px;
    left: 0;
    width: 100%;
    background-color: var(--white);
    padding: 20px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }



  .homepage .menu-toggle {
    display: block;
  }

  /* 项目广场页面响应式样式 */
  .plaza-header .nav-links {
    display: none;
  }

  .plaza-header .nav-links.active {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 80px;
    left: 0;
    width: 100%;
    background-color: var(--white);
    padding: 20px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }

  .plaza-header .menu-toggle {
    display: block;
  }

  /* 移动端用户菜单样式 */
  .homepage .user-menu button span {
    display: none !important;
  }

  .plaza-header .user-menu button span {
    display: none !important;
  }

  .homepage .hero .container {
    flex-direction: column;
    text-align: center;
  }

  .homepage .hero-content {
    margin-bottom: 50px;
  }

  .homepage .hero-content h1 {
    font-size: 2.5rem;
  }

  .homepage .hero-buttons {
    justify-content: center;
  }

  .homepage .features-grid {
    grid-template-columns: 1fr;
  }

  .homepage .download-options {
    flex-direction: column;
    align-items: center;
  }

  .homepage .contact-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .homepage .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .homepage .screenshot-container {
    max-width: 90%;
  }

  .homepage .slider-btn {
    width: 35px;
    height: 35px;
  }

  .homepage .prev-btn {
    left: -10px;
  }

  .homepage .next-btn {
    right: -10px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}
