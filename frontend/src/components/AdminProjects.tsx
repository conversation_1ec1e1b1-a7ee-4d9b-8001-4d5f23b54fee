import React, { useState, useEffect } from 'react';
import {
    Search,
    Award,
    Eye,
    User,
    X
} from 'lucide-react';
import { apiClient } from '../api';
import type { ProjectWithCreator, ProjectDetail, AdminProjectUpdate } from '../types';

const AdminProjects: React.FC = () => {
    const [projects, setProjects] = useState<ProjectWithCreator[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedProject, setSelectedProject] = useState<ProjectDetail | null>(null);
    const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

    useEffect(() => {
        loadProjects();
    }, []);

    const loadProjects = async () => {
        try {
            setLoading(true);
            const data = await apiClient.getAdminProjects();
            setProjects(data);
        } catch (error) {
            console.error('Failed to load projects:', error);
            setMessage({ type: 'error', text: '加载项目失败' });
        } finally {
            setLoading(false);
        }
    };

    const handleScoreChange = async (projectId: number, score: number) => {
        try {
            const updateData: AdminProjectUpdate = { score };
            await apiClient.updateAdminProject(projectId, updateData);
            setMessage({ type: 'success', text: '评分更新成功' });
            loadProjects();
        } catch (error) {
            setMessage({ type: 'error', text: '评分更新失败' });
        }
    };

    const handleFeaturedToggle = async (projectId: number, isFeatured: boolean) => {
        try {
            const updateData: AdminProjectUpdate = { is_featured: isFeatured };
            await apiClient.updateAdminProject(projectId, updateData);
            setMessage({ type: 'success', text: `${isFeatured ? '标记' : '取消'}官方精选成功` });
            loadProjects();
        } catch (error) {
            setMessage({ type: 'error', text: '官方精选状态更新失败' });
        }
    };

    const handleViewDetail = async (projectId: number) => {
        try {
            const detail = await apiClient.getAdminProjectDetail(projectId);
            setSelectedProject(detail);
            setShowDetailModal(true);
        } catch (error) {
            setMessage({ type: 'error', text: '获取项目详情失败' });
        }
    };

    const filteredProjects = projects.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        project.creator_username.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-CN');
    };

    return (
        <div className="p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">项目管理</h1>
                <p className="text-gray-600">管理所有用户的项目，进行评分和标记官方精选</p>
            </div>

            {/* 消息提示 */}
            {message && (
                <div className={`mb-6 p-4 rounded-md ${
                    message.type === 'success'
                        ? 'bg-green-50 text-green-800 border border-green-200'
                        : 'bg-red-50 text-red-800 border border-red-200'
                }`}>
                    {message.text}
                </div>
            )}

            {/* 搜索框 */}
            <div className="mb-6">
                <div className="relative max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                        type="text"
                        placeholder="搜索项目或用户..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
            </div>

            {/* 项目列表 */}
            {loading ? (
                <div className="text-center py-12">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-gray-600">加载中...</p>
                </div>
            ) : (
                <div className="bg-white shadow-sm rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    项目信息
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    创建者
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    评分
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    状态
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {filteredProjects.map((project) => (
                                <tr key={project.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4">
                                        <div>
                                            <div className="flex items-center">
                                                <h3 className="text-sm font-medium text-gray-900">
                                                    {project.name}
                                                </h3>
                                                {project.is_featured && (
                                                    <Award className="ml-2 h-4 w-4 text-yellow-500" />
                                                )}
                                            </div>
                                            {project.description && (
                                                <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                                                    {project.description}
                                                </p>
                                            )}
                                            <p className="text-xs text-gray-400 mt-1">
                                                创建于 {formatDate(project.created_at)}
                                            </p>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4">
                                        <div className="flex items-center">
                                            {project.creator_avatar_url ? (
                                                <img
                                                    src={project.creator_avatar_url}
                                                    alt={project.creator_username}
                                                    className="h-8 w-8 rounded-full mr-3"
                                                />
                                            ) : (
                                                <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                                                    <User className="h-4 w-4 text-gray-600" />
                                                </div>
                                            )}
                                            <span className="text-sm text-gray-900">{project.creator_username}</span>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4">
                                        <input
                                            type="number"
                                            min="0"
                                            max="100"
                                            value={project.score}
                                            onChange={(e) => handleScoreChange(project.id, parseInt(e.target.value) || 0)}
                                            className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </td>
                                    <td className="px-6 py-4">
                                        <label className="flex items-center">
                                            <input
                                                type="checkbox"
                                                checked={project.is_featured}
                                                onChange={(e) => handleFeaturedToggle(project.id, e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            />
                                            <span className="ml-2 text-sm text-gray-700">官方精选</span>
                                        </label>
                                    </td>
                                    <td className="px-6 py-4">
                                        <button
                                            onClick={() => handleViewDetail(project.id)}
                                            className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        >
                                            <Eye className="h-4 w-4 mr-1" />
                                            查看详情
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}

            {/* 项目详情模态框 */}
            {showDetailModal && selectedProject && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 w-4/5 max-w-4xl bg-white rounded-lg shadow-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-medium text-gray-900">项目详情</h3>
                            <button
                                onClick={() => setShowDetailModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <X className="h-6 w-6" />
                            </button>
                        </div>
                        
                        <div className="space-y-6">
                            {/* 基本信息 */}
                            <div>
                                <h4 className="text-md font-medium text-gray-900 mb-2">基本信息</h4>
                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">项目名称</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedProject.name}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">创建者</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedProject.creator_username}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">评分</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedProject.score}</p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">官方精选</label>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {selectedProject.is_featured ? '是' : '否'}
                                            </p>
                                        </div>
                                    </div>
                                    {selectedProject.description && (
                                        <div className="mt-4">
                                            <label className="block text-sm font-medium text-gray-700">项目描述</label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedProject.description}</p>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* 配置文件内容 */}
                            {selectedProject.config_file_content && (
                                <div>
                                    <h4 className="text-md font-medium text-gray-900 mb-2">配置文件内容</h4>
                                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96">
                                        <pre className="text-sm whitespace-pre-wrap">
                                            {selectedProject.config_file_content}
                                        </pre>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminProjects;
