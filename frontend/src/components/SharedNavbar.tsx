import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { generateAvatarUrl } from '../utils/avatar';

interface SharedNavbarProps {
  variant?: 'homepage' | 'plaza';
}

const SharedNavbar: React.FC<SharedNavbarProps> = ({ variant = 'homepage' }) => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [avatarError, setAvatarError] = useState(false);
  // 处理用户菜单点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuOpen) {
        const target = event.target as Element;
        if (!target.closest('.user-menu')) {
          setUserMenuOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [userMenuOpen]);

  // 处理登出
  const handleLogout = () => {
    logout();
    setUserMenuOpen(false);
    navigate('/');
  };

  // 处理头像加载错误
  const handleAvatarError = () => {
    setAvatarError(true);
  };

  // 获取头像URL
  const getAvatarSrc = () => {
    if (avatarError || !user?.avatar_url) {
      return generateAvatarUrl(user?.username || '');
    }
    return user.avatar_url;
  };

  useEffect(() => {
    // 移动端菜单切换（两个页面都需要）
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    const handleMenuToggle = () => {
      navLinks?.classList.toggle('active');
    };

    menuToggle?.addEventListener('click', handleMenuToggle);

    if (variant === 'homepage') {
      // 导航栏滚动效果（仅在首页）
      const header = document.querySelector('header');

      const handleScroll = () => {
        if (window.scrollY > 50) {
          header?.classList.add('scrolled');
        } else {
          header?.classList.remove('scrolled');
        }
      };

      window.addEventListener('scroll', handleScroll);

      // 清理事件监听器
      return () => {
        window.removeEventListener('scroll', handleScroll);
        menuToggle?.removeEventListener('click', handleMenuToggle);
      };
    }

    // 清理事件监听器（项目广场页面）
    return () => {
      menuToggle?.removeEventListener('click', handleMenuToggle);
    };
  }, [variant]);

  if (variant === 'plaza') {
    // 项目广场页面的导航栏（使用首页样式但适配项目广场）
    return (
      <header className="plaza-header">
        <nav className="container">
          <div className="logo">
            <Link to="/" className="logo-link">
              <img src="/logo.png" alt="P-Box Logo" />
              <span>P-Box</span>
            </Link>
          </div>
          <div className="nav-links">
            <Link to="/">首页</Link>
            <Link to="/plaza">项目广场</Link>
            {isAuthenticated && user ? (
              <div className="user-menu relative">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors"
                >
                  <img
                    src={getAvatarSrc()}
                    alt={user.username}
                    className="w-8 h-8 rounded-full border-2 border-gray-300"
                    onError={handleAvatarError}
                  />
                  <span className="hidden md:inline">{user.username}</span>
                  <i className="fas fa-chevron-down text-xs"></i>
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      <div className="font-medium">{user.username}</div>
                      <div className="text-gray-500 text-xs">{user.email}</div>
                      {user.is_admin && (
                        <div className="text-xs text-blue-600 mt-1">管理员</div>
                      )}
                    </div>

                    <Link
                      to="/admin/projects"
                      onClick={() => setUserMenuOpen(false)}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-folder mr-2"></i>
                      我的项目
                    </Link>

                    <Link
                      to="/admin/profile"
                      onClick={() => setUserMenuOpen(false)}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-user mr-2"></i>
                      个人中心
                    </Link>

                    {user.is_admin && (
                      <Link
                        to="/admin"
                        onClick={() => setUserMenuOpen(false)}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <i className="fas fa-cog mr-2"></i>
                        管理后台
                      </Link>
                    )}

                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-sign-out-alt mr-2"></i>
                      退出登录
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link to="/login" className="nav-admin-link">登录</Link>
            )}
          </div>
          <div className="menu-toggle">
            <i className="fas fa-bars"></i>
          </div>
        </nav>
      </header>
    );
  }

  // 首页的导航栏
  return (
    <header>
      <nav className="container">
        <div className="logo">
          <img src="/logo.png" alt="P-Box Logo" />
          <span>P-Box</span>
        </div>
        <div className="nav-links">
          <a href="#features">功能特点</a>
          <a href="#screenshots">界面展示</a>
          <a href="#download">立即下载</a>
          <a href="#contact">联系我们</a>
          <Link to="/plaza">项目广场</Link>
          {isAuthenticated && user ? (
            <div className="user-menu relative">
              <button
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                <img
                  src={getAvatarSrc()}
                  alt={user.username}
                  className="w-8 h-8 rounded-full border-2 border-gray-300"
                  onError={handleAvatarError}
                />
                <span className="hidden md:inline">{user.username}</span>
                <i className="fas fa-chevron-down text-xs"></i>
              </button>

              {userMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border">
                  <div className="px-4 py-2 text-sm text-gray-700 border-b">
                    <div className="font-medium">{user.username}</div>
                    <div className="text-gray-500 text-xs">{user.email}</div>
                    {user.is_admin && (
                      <div className="text-xs text-blue-600 mt-1">管理员</div>
                    )}
                  </div>

                  <Link
                    to="/admin/projects"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <i className="fas fa-folder mr-2"></i>
                    我的项目
                  </Link>

                  <Link
                    to="/admin/profile"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <i className="fas fa-user mr-2"></i>
                    个人中心
                  </Link>

                  {user.is_admin && (
                    <Link
                      to="/admin"
                      onClick={() => setUserMenuOpen(false)}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <i className="fas fa-cog mr-2"></i>
                      管理后台
                    </Link>
                  )}

                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <i className="fas fa-sign-out-alt mr-2"></i>
                    退出登录
                  </button>
                </div>
              )}
            </div>
          ) : (
            <Link to="/login" className="nav-admin-link">登录</Link>
          )}
        </div>
        <div className="menu-toggle">
          <i className="fas fa-bars"></i>
        </div>
      </nav>
    </header>
  );
};

export default SharedNavbar;
